cmake_minimum_required(VERSION 3.0.2)
project(teb_planner_service)

find_package(catkin REQUIRED COMPONENTS
  roscpp
  rospy
  std_msgs
  teb_local_planner 
  geometry_msgs 
  nav_msgs 
  costmap_converter
)


## Generate services in the 'srv' folder
add_service_files(
  FILES
  PlanTrajectory.srv
)


generate_messages(
  DEPENDENCIES geometry_msgs nav_msgs costmap_converter
)

catkin_package(
  CATKIN_DEPENDS roscpp rospy std_msgs teb_local_planner geometry_msgs nav_msgs costmap_converter
)



include_directories(
# include
  ${catkin_INCLUDE_DIRS}
)

add_executable(test_teb_service src/teb_service.cpp)
target_link_libraries(test_teb_service ${catkin_LIBRARIES})

install(TARGETS test_teb_service
  RUNTIME DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION}
)

